'use strict';

const newsController = ({ strapi }) => ({
  // Get all news categories
  async getNewsCategories(ctx) {
    try {
      const categories = await strapi.entityService.findMany(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        {
          populate: {
            image: {
              fields: ['id', 'name', 'url', 'alternativeText'],
            },
          },
          sort: { createdAt: 'desc' },
        }
      );

      // Transform data to include full image URLs
      const transformedCategories = categories.map((category) => ({
        ...category,
        image: category.image
          ? {
              ...category.image,
              url: category.image.url?.startsWith('http')
                ? category.image.url
                : `${strapi.config.server.url}${category.image.url}`,
            }
          : null,
      }));

      ctx.body = {
        success: true,
        data: transformedCategories,
        message: 'News categories retrieved successfully',
      };
    } catch (error: any) {
      console.error('Error fetching news categories:', error);
      ctx.throw(500, `Failed to fetch news categories: ${error.message}`);
    }
  },

  // Create news category
  async createNewsCategory(ctx) {
    try {
      const { name, description, image, isActive } =
        ctx.request.body.data || ctx.request.body;

      if (!name || name.trim() === '') {
        ctx.throw(400, 'Category name is required');
      }

      // Check for duplicate names
      const existingCategory = await strapi.entityService.findMany(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        {
          filters: {
            name: {
              $eqi: name.trim(),
            },
          },
        }
      );

      if (existingCategory && existingCategory.length > 0) {
        ctx.throw(
          400,
          `News category with this name already exists: "${existingCategory[0].name}"`
        );
      }

      const category = await strapi.entityService.create(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        {
          data: {
            name,
            description: description || '',
            image: image || null,
            isActive: isActive !== undefined ? isActive : true,
          } as any,
          populate: {
            image: {
              fields: ['id', 'name', 'url', 'alternativeText'],
            },
          },
        }
      );

      // Transform image URL
      const transformedCategory = {
        ...category,
        image: category.image
          ? {
              ...category.image,
              url: category.image.url?.startsWith('http')
                ? category.image.url
                : `${strapi.config.server.url}${category.image.url}`,
            }
          : null,
      };

      ctx.body = {
        success: true,
        data: transformedCategory,
        message: 'News category created successfully',
      };
    } catch (error: any) {
      console.error('Error creating news category:', error);
      ctx.throw(500, `Failed to create news category: ${error.message}`);
    }
  },

  // Update news category
  async updateNewsCategory(ctx) {
    try {
      const { id } = ctx.params;
      const { name, description, image, isActive } =
        ctx.request.body.data || ctx.request.body;

      if (!name || name.trim() === '') {
        ctx.throw(400, 'Category name is required');
      }

      // Check for duplicate names (excluding current category)
      const existingCategory = await strapi.entityService.findMany(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        {
          filters: {
            name: {
              $eqi: name.trim(),
            },
            id: {
              $ne: id,
            },
          },
        }
      );

      if (existingCategory && existingCategory.length > 0) {
        ctx.throw(
          400,
          `News category with this name already exists: "${existingCategory[0].name}"`
        );
      }

      // Prepare update data
      const updateData: any = {
        name,
        description: description || '',
        isActive: isActive !== undefined ? isActive : true,
      };

      // Handle image update logic
      if (image !== undefined) {
        // Image field is explicitly provided (could be new image ID or null to remove)
        updateData.image = image;
      }
      // If image is undefined, don't include it in update to preserve existing image

      const category = await strapi.entityService.update(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        id,
        {
          data: updateData,
          populate: {
            image: {
              fields: ['id', 'name', 'url', 'alternativeText'],
            },
          },
        }
      );

      // Transform image URL
      const transformedCategory = {
        ...category,
        image: category.image
          ? {
              ...category.image,
              url: category.image.url?.startsWith('http')
                ? category.image.url
                : `${strapi.config.server.url}${category.image.url}`,
            }
          : null,
      };

      ctx.body = {
        success: true,
        data: transformedCategory,
        message: 'News category updated successfully',
      };
    } catch (error: any) {
      console.error('Error updating news category:', error);
      ctx.throw(500, `Failed to update news category: ${error.message}`);
    }
  },

  // Delete news category
  async deleteNewsCategory(ctx) {
    try {
      const { id } = ctx.params;

      // Check if category is being used by any articles
      const articlesUsingCategory = await strapi.entityService.findMany(
        'api::bai-viet.bai-viet',
        {
          filters: {
            danh_muc: {
              id: {
                $eq: id,
              },
            },
          },
          limit: 1,
        }
      );

      if (articlesUsingCategory && articlesUsingCategory.length > 0) {
        ctx.throw(400, 'Cannot delete category that is being used by articles');
      }

      await strapi.entityService.delete(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        id
      );

      ctx.body = {
        success: true,
        message: 'News category deleted successfully',
      };
    } catch (error: any) {
      console.error('Error deleting news category:', error);
      ctx.throw(500, `Failed to delete news category: ${error.message}`);
    }
  },

  // Get all news articles
  async getNewsArticles(ctx) {
    try {
      const articles = await strapi.entityService.findMany(
        'api::bai-viet.bai-viet',
        {
          populate: {
            image: {
              fields: ['id', 'name', 'url', 'alternativeText'],
            },
            danh_muc: {
              fields: ['id', 'name'],
            },
          },
          sort: { createdAt: 'desc' },
        }
      );

      // Transform data to include full image URLs
      const transformedArticles = articles.map((article: any) => ({
        ...article,
        image: article.image
          ? {
              ...article.image,
              url: article.image.url?.startsWith('http')
                ? article.image.url
                : `${strapi.config.server.url}${article.image.url}`,
            }
          : null,
      }));

      ctx.body = {
        success: true,
        data: transformedArticles,
        message: 'News articles retrieved successfully',
      };
    } catch (error: any) {
      console.error('Error fetching news articles:', error);
      ctx.throw(500, `Failed to fetch news articles: ${error.message}`);
    }
  },

  // Get news article detail
  async getNewsArticleDetail(ctx) {
    try {
      const { id } = ctx.params;

      const article = await strapi.entityService.findOne(
        'api::bai-viet.bai-viet',
        id,
        {
          populate: ['image', 'danh_muc'],
        }
      );

      if (!article) {
        ctx.throw(404, 'News article not found');
      }

      ctx.body = {
        success: true,
        data: article,
        message: 'News article retrieved successfully',
      };
    } catch (error: any) {
      console.error('Error fetching news article:', error);
      ctx.throw(500, `Failed to fetch news article: ${error.message}`);
    }
  },

  // Create news article
  async createNewsArticle(ctx) {
    try {
      const { title, content, image, danh_muc, hot, isActive } =
        ctx.request.body.data || ctx.request.body;

      if (!title || title.trim() === '') {
        ctx.throw(400, 'Article title is required');
      }

      if (!image) {
        ctx.throw(400, 'Article image is required');
      }

      const article = await strapi.entityService.create(
        'api::bai-viet.bai-viet',
        {
          data: {
            title,
            content: content || '',
            image,
            danh_muc: danh_muc || null,
            hot: hot !== undefined ? hot : false,
            isActive: isActive !== undefined ? isActive : true,
          } as any,
        }
      );

      ctx.body = {
        success: true,
        data: article,
        message: 'News article created successfully',
      };
    } catch (error: any) {
      console.error('Error creating news article:', error);
      ctx.throw(500, `Failed to create news article: ${error.message}`);
    }
  },

  // Update news article
  async updateNewsArticle(ctx) {
    try {
      const { id } = ctx.params;
      const { title, content, image, danh_muc, hot, isActive } =
        ctx.request.body.data || ctx.request.body;

      if (!title || title.trim() === '') {
        ctx.throw(400, 'Article title is required');
      }

      const article = await strapi.entityService.update(
        'api::bai-viet.bai-viet',
        id,
        {
          data: {
            title,
            content: content || '',
            image,
            danh_muc: danh_muc || null,
            hot: hot !== undefined ? hot : false,
            isActive: isActive !== undefined ? isActive : true,
          } as any,
        }
      );

      ctx.body = {
        success: true,
        data: article,
        message: 'News article updated successfully',
      };
    } catch (error: any) {
      console.error('Error updating news article:', error);
      ctx.throw(500, `Failed to update news article: ${error.message}`);
    }
  },

  // Delete news article
  async deleteNewsArticle(ctx) {
    try {
      const { id } = ctx.params;

      await strapi.entityService.delete('api::bai-viet.bai-viet', id);

      ctx.body = {
        success: true,
        message: 'News article deleted successfully',
      };
    } catch (error: any) {
      console.error('Error deleting news article:', error);
      ctx.throw(500, `Failed to delete news article: ${error.message}`);
    }
  },
});

module.exports = newsController;
