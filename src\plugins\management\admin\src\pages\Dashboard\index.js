// const React = require('react');
// const { useState, useEffect } = React;
// const {
//   Spin,
//   Row,
//   Col,
//   Typography,
//   Space,
//   Table: AntTable,
//   Empty,
// } = require('antd');
// const {
//   ReloadOutlined,
//   DollarOutlined,
//   ShoppingCartOutlined,
//   UserOutlined,
//   AppstoreOutlined,
//   RiseOutlined,
//   CrownOutlined,
//   FireOutlined,
// } = require('@ant-design/icons');
// const { useFetchClient } = require('@strapi/helper-plugin');
// const {
//   PageContainer,
//   Card,
//   CardContent,
//   PageHeader,
//   StatsGrid,
//   StatsCard,
//   Button,
//   StyledTable,
// } = require('../../components/shared');
// // const {
// //   BarChart,
// //   Bar,
// //   XAxis,
// //   YAxis,
// //   <PERSON><PERSON>ianGrid,
// //   Tooltip,
// //   Legend,
// //   ResponsiveContainer,
// //   Line<PERSON><PERSON>,
// //   Line,
// //   PieChart,
// //   Pie,
// //   Cell,
// //   AreaChart,
// //   Area,
// // } = require('recharts');
// const {
//   TrendingUp,
//   BarChart3,
//   Users,
//   Package,
//   Award,
//   ShoppingCart,
// } = require('lucide-react');

// const { Title } = Typography;

// const Dashboard = () => {
//   const [loading, setLoading] = useState(false);
//   const [stats, setStats] = useState({
//     totalRevenue: 0,
//     totalOrders: 0,
//     totalUsers: 0,
//     totalProducts: 0,
//   });
//   const [revenueData, setRevenueData] = useState([]);
//   const [topCustomers, setTopCustomers] = useState([]);
//   const [bestSellingProducts, setBestSellingProducts] = useState([]);
//   const [recentOrders, setRecentOrders] = useState([]);

//   const { get } = useFetchClient();

//   // Fetch dashboard data
//   const fetchDashboardData = async () => {
//     setLoading(true);
//     try {
//       // Fetch KPI data
//       const kpiResponse = await get('/management/dashboard/kpi');
//       setStats(kpiResponse.data);

//       // Fetch chart data
//       const chartResponse = await get('/management/dashboard/charts/revenue');
//       setRevenueData(chartResponse.data.data || []);

//       // Fetch top customers
//       const customersResponse = await get(
//         '/management/dashboard/top-customers'
//       );
//       setTopCustomers(customersResponse.data.data || []);

//       // Fetch best selling products
//       const productsResponse = await get(
//         '/management/dashboard/best-selling-products'
//       );
//       setBestSellingProducts(productsResponse.data.data || []);

//       // Fetch recent orders
//       const ordersResponse = await get('/management/orders', {
//         params: {
//           page: 1,
//           pageSize: 5,
//           sortBy: 'createdAt',
//           sortOrder: 'desc',
//         },
//       });

//       // Transform order data to match interface
//       const transformedOrders = (ordersResponse.data.data || []).map(
//         (order) => ({
//           id: order.id,
//           code: order.code,
//           customerName: order.customer?.name || 'N/A',
//           customerPhone: order.customer?.phone || 'N/A',
//           total: order.priceAfterTax,
//           status: order.statusOrder,
//           productCount: order.products?.length || 0,
//           createdAt: order.createdAt,
//         })
//       );

//       setRecentOrders(transformedOrders);
//     } catch (error) {
//       console.error('Error fetching dashboard data:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     fetchDashboardData();
//   }, []);

//   // Stats data for cards
//   const statsData = [
//     {
//       title: 'Doanh thu',
//       value: new Intl.NumberFormat('vi-VN', {
//         style: 'currency',
//         currency: 'VND',
//       }).format(stats.totalRevenue),
//       icon: DollarOutlined,
//       color: 'bg-green',
//     },
//     {
//       title: 'Tổng đơn hàng',
//       value: stats.totalOrders.toLocaleString('vi-VN'),
//       icon: ShoppingCartOutlined,
//       color: 'bg-blue',
//     },
//     {
//       title: 'Tổng đại lý',
//       value: stats.totalUsers.toLocaleString('vi-VN'),
//       icon: UserOutlined,
//       color: 'bg-blue-dark',
//     },
//     {
//       title: 'Tổng sản phẩm',
//       value: stats.totalProducts.toLocaleString('vi-VN'),
//       icon: AppstoreOutlined,
//       color: 'bg-red',
//     },
//   ];

//   // Top customers table columns
//   const customerColumns = [
//     {
//       title: 'Tên khách hàng',
//       dataIndex: 'name',
//       key: 'name',
//     },
//     {
//       title: 'Email',
//       dataIndex: 'email',
//       key: 'email',
//     },
//     {
//       title: 'Số đơn hàng',
//       dataIndex: 'totalOrders',
//       key: 'totalOrders',
//       render: (value) => value.toLocaleString('vi-VN'),
//     },
//     {
//       title: 'Tổng chi tiêu',
//       dataIndex: 'totalSpent',
//       key: 'totalSpent',
//       render: (value) =>
//         new Intl.NumberFormat('vi-VN', {
//           style: 'currency',
//           currency: 'VND',
//         }).format(value),
//     },
//   ];

//   // Best selling products table columns
//   const productColumns = [
//     {
//       title: 'Tên sản phẩm',
//       dataIndex: 'name',
//       key: 'name',
//     },
//     {
//       title: 'Đã bán',
//       dataIndex: 'sold',
//       key: 'sold',
//       render: (value) => value.toLocaleString('vi-VN'),
//     },
//     {
//       title: 'Doanh thu',
//       dataIndex: 'revenue',
//       key: 'revenue',
//       render: (value) =>
//         new Intl.NumberFormat('vi-VN', {
//           style: 'currency',
//           currency: 'VND',
//         }).format(value),
//     },
//   ];

//   // Recent orders table columns
//   const orderColumns = [
//     {
//       title: 'Mã đơn hàng',
//       dataIndex: 'code',
//       key: 'code',
//       width: 120,
//       render: (value) =>
//         React.createElement(
//           'div',
//           { style: { fontWeight: 600, color: '#1e293b' } },
//           value
//         ),
//     },
//     {
//       title: 'Khách hàng',
//       dataIndex: 'customerName',
//       key: 'customerName',
//       width: 150,
//     },
//     {
//       title: 'Số điện thoại',
//       dataIndex: 'customerPhone',
//       key: 'customerPhone',
//       width: 120,
//     },
//     {
//       title: 'Tổng tiền',
//       dataIndex: 'total',
//       key: 'total',
//       width: 120,
//       render: (value) =>
//         React.createElement(
//           'div',
//           { style: { fontWeight: 600, color: '#000' } },
//           new Intl.NumberFormat('vi-VN', {
//             style: 'currency',
//             currency: 'VND',
//           }).format(value)
//         ),
//     },
//     {
//       title: 'Số sản phẩm',
//       dataIndex: 'productCount',
//       key: 'productCount',
//       width: 100,
//       render: (count) =>
//         React.createElement(
//           'div',
//           { style: { textAlign: 'center', fontWeight: 500 } },
//           count || 0
//         ),
//     },
//     {
//       title: 'Trạng thái đơn hàng',
//       dataIndex: 'status',
//       key: 'status',
//       width: 140,
//       render: (status) => {
//         const statusMap = {
//           'Chờ xác nhận': {
//             text: 'Chờ xác nhận',
//             color: '#f59e0b',
//             bg: '#fffbeb',
//           },
//           'Chờ giao hàng': {
//             text: 'Chờ giao hàng',
//             color: '#3b82f6',
//             bg: '#eff6ff',
//           },
//           'Đang giao hàng': {
//             text: 'Đang giao hàng',
//             color: '#8b5cf6',
//             bg: '#f3f4f6',
//           },
//           'Đã hoàn thành': {
//             text: 'Hoàn thành',
//             color: '#10b981',
//             bg: '#ecfdf5',
//           },
//           'Đã hủy': { text: 'Đã hủy', color: '#ef4444', bg: '#fef2f2' },
//         };
//         const statusInfo = statusMap[status] || {
//           text: status,
//           color: '#6b7280',
//           bg: '#f9fafb',
//         };
//         return React.createElement(
//           'span',
//           {
//             style: {
//               color: statusInfo.color,
//               fontWeight: 500,
//               padding: '4px 8px',
//               borderRadius: '4px',
//               backgroundColor: statusInfo.bg,
//               fontSize: '12px',
//             },
//           },
//           statusInfo.text
//         );
//       },
//     },
//     {
//       title: 'Ngày tạo',
//       dataIndex: 'createdAt',
//       key: 'createdAt',
//       width: 120,
//       render: (date) =>
//         React.createElement(
//           'div',
//           { style: { fontSize: '13px', color: '#6b7280' } },
//           new Date(date).toLocaleDateString('vi-VN', {
//             day: '2-digit',
//             month: '2-digit',
//             year: 'numeric',
//           })
//         ),
//     },
//   ];

//   return React.createElement(
//     PageContainer,
//     null,
//     React.createElement(
//       Spin,
//       { spinning: loading, tip: 'Đang tải dữ liệu...' },
//       [
//         // Stats Cards
//         React.createElement(
//           StatsGrid,
//           { key: 'stats' },
//           statsData.map((stat, index) =>
//             React.createElement(StatsCard, {
//               key: index,
//               title: stat.title,
//               value: stat.value,
//               icon: React.createElement(stat.icon),
//               color: stat.color,
//             })
//           )
//         ),

//         // Charts Section
//         React.createElement(
//           Row,
//           { key: 'charts', gutter: [16, 16], style: { marginBottom: 24 } },
//           [
//             // Revenue Chart
//             React.createElement(
//               Col,
//               { key: 'revenue-chart', xs: 24, lg: 16 },
//               React.createElement(
//                 Card,
//                 null,
//                 React.createElement(CardContent, null, [
//                   React.createElement(
//                     'div',
//                     { key: 'title', style: { marginBottom: 16 } },
//                     React.createElement(
//                       Title,
//                       {
//                         level: 4,
//                         style: {
//                           margin: 0,
//                           fontFamily: "'Be Vietnam Pro', sans-serif",
//                         },
//                       },
//                       React.createElement(Space, null, [
//                         React.createElement(RiseOutlined, {
//                           key: 'icon',
//                           style: { color: '#3b82f6' },
//                         }),
//                         'Thống kê doanh thu',
//                       ])
//                     )
//                   ),
//                   React.createElement(
//                     ResponsiveContainer,
//                     { key: 'chart', width: '100%', height: 300 },
//                     React.createElement(AreaChart, { data: revenueData }, [
//                       React.createElement(CartesianGrid, {
//                         key: 'grid',
//                         strokeDasharray: '3 3',
//                       }),
//                       React.createElement(XAxis, {
//                         key: 'xaxis',
//                         dataKey: 'month',
//                       }),
//                       React.createElement(YAxis, { key: 'yaxis' }),
//                       React.createElement(Tooltip, {
//                         key: 'tooltip',
//                         formatter: (value, name) => [
//                           name === 'revenue'
//                             ? new Intl.NumberFormat('vi-VN', {
//                                 style: 'currency',
//                                 currency: 'VND',
//                               }).format(Number(value))
//                             : value,
//                           name === 'revenue' ? 'Doanh thu' : 'Đơn hàng',
//                         ],
//                       }),
//                       React.createElement(Area, {
//                         key: 'area',
//                         type: 'monotone',
//                         dataKey: 'revenue',
//                         stroke: '#3b82f6',
//                         fill: '#3b82f6',
//                         fillOpacity: 0.3,
//                         name: 'Doanh thu',
//                       }),
//                     ])
//                   ),
//                 ])
//               )
//             ),
//             // Top Customers
//             React.createElement(
//               Col,
//               { key: 'top-customers', xs: 24, lg: 8 },
//               React.createElement(
//                 Card,
//                 { style: { minHeight: '392px' } },
//                 React.createElement(CardContent, null, [
//                   React.createElement(
//                     'div',
//                     { key: 'title', style: { marginBottom: 16 } },
//                     React.createElement(
//                       Title,
//                       {
//                         level: 4,
//                         style: {
//                           margin: 0,
//                           fontFamily: "'Be Vietnam Pro', sans-serif",
//                         },
//                       },
//                       React.createElement(Space, null, [
//                         React.createElement(CrownOutlined, {
//                           key: 'icon',
//                           style: { color: '#f59e0b' },
//                         }),
//                         'Top khách hàng',
//                       ])
//                     )
//                   ),
//                   topCustomers.length > 0
//                     ? React.createElement(
//                         StyledTable,
//                         { key: 'table' },
//                         React.createElement(AntTable, {
//                           dataSource: topCustomers,
//                           columns: customerColumns,
//                           pagination: false,
//                           size: 'small',
//                           rowKey: 'id',
//                           locale: {
//                             emptyText: React.createElement(Empty, {
//                               image: Empty.PRESENTED_IMAGE_SIMPLE,
//                               description: React.createElement(
//                                 'span',
//                                 {
//                                   style: {
//                                     color: '#64748b',
//                                     fontFamily: "'Be Vietnam Pro', sans-serif",
//                                   },
//                                 },
//                                 'Không có dữ liệu'
//                               ),
//                             }),
//                           },
//                           style: {
//                             fontFamily: "'Be Vietnam Pro', sans-serif",
//                           },
//                         })
//                       )
//                     : React.createElement(Empty, {
//                         key: 'empty',
//                         description: 'Không có dữ liệu',
//                       }),
//                 ])
//               )
//             ),
//           ]
//         ),

//         // Tables Section
//         React.createElement(
//           Row,
//           { key: 'tables', gutter: [16, 16], style: { marginBottom: 24 } },
//           [
//             // Best Selling Products
//             React.createElement(
//               Col,
//               { key: 'best-products', xs: 24, lg: 8 },
//               React.createElement(
//                 Card,
//                 { style: { minHeight: '392px' } },
//                 React.createElement(CardContent, null, [
//                   React.createElement(
//                     'div',
//                     { key: 'title', style: { marginBottom: 16 } },
//                     React.createElement(
//                       Title,
//                       {
//                         level: 4,
//                         style: {
//                           margin: 0,
//                           fontFamily: "'Be Vietnam Pro', sans-serif",
//                         },
//                       },
//                       React.createElement(Space, null, [
//                         React.createElement(FireOutlined, {
//                           key: 'icon',
//                           style: { color: '#ef4444' },
//                         }),
//                         'Sản phẩm bán chạy',
//                       ])
//                     )
//                   ),
//                   bestSellingProducts.length > 0
//                     ? React.createElement(
//                         StyledTable,
//                         { key: 'table' },
//                         React.createElement(AntTable, {
//                           dataSource: bestSellingProducts,
//                           columns: productColumns,
//                           pagination: false,
//                           size: 'small',
//                           rowKey: 'id',
//                           locale: {
//                             emptyText: React.createElement(Empty, {
//                               image: Empty.PRESENTED_IMAGE_SIMPLE,
//                               description: React.createElement(
//                                 'span',
//                                 {
//                                   style: {
//                                     color: '#64748b',
//                                     fontFamily: "'Be Vietnam Pro', sans-serif",
//                                   },
//                                 },
//                                 'Không có dữ liệu'
//                               ),
//                             }),
//                           },
//                           style: {
//                             fontFamily: "'Be Vietnam Pro', sans-serif",
//                           },
//                         })
//                       )
//                     : React.createElement(Empty, {
//                         key: 'empty',
//                         description: 'Không có dữ liệu',
//                       }),
//                 ])
//               )
//             ),

//             // Orders Chart
//             React.createElement(
//               Col,
//               { key: 'orders-chart', xs: 24, lg: 16 },
//               React.createElement(
//                 Card,
//                 null,
//                 React.createElement(CardContent, null, [
//                   React.createElement(
//                     'div',
//                     { key: 'title', style: { marginBottom: 16 } },
//                     React.createElement(
//                       Title,
//                       {
//                         level: 4,
//                         style: {
//                           margin: 0,
//                           fontFamily: "'Be Vietnam Pro', sans-serif",
//                         },
//                       },
//                       React.createElement(Space, null, [
//                         React.createElement(ShoppingCartOutlined, {
//                           key: 'icon',
//                           style: { color: '#10b981' },
//                         }),
//                         'Đơn hàng',
//                       ])
//                     )
//                   ),
//                   React.createElement(
//                     ResponsiveContainer,
//                     { key: 'chart', width: '100%', height: 300 },
//                     React.createElement(BarChart, { data: revenueData }, [
//                       React.createElement(CartesianGrid, {
//                         key: 'grid',
//                         strokeDasharray: '3 3',
//                       }),
//                       React.createElement(XAxis, {
//                         key: 'xaxis',
//                         dataKey: 'month',
//                       }),
//                       React.createElement(YAxis, { key: 'yaxis' }),
//                       React.createElement(Tooltip, { key: 'tooltip' }),
//                       React.createElement(Legend, { key: 'legend' }),
//                       React.createElement(Bar, {
//                         key: 'bar',
//                         dataKey: 'orders',
//                         fill: '#10b981',
//                         name: 'Số đơn hàng',
//                       }),
//                     ])
//                   ),
//                 ])
//               )
//             ),
//           ]
//         ),

//         // Recent Orders
//         React.createElement(
//           Card,
//           { key: 'recent-orders' },
//           React.createElement(CardContent, null, [
//             React.createElement(
//               'div',
//               { key: 'title', style: { marginBottom: 16 } },
//               React.createElement(
//                 Title,
//                 {
//                   level: 4,
//                   style: {
//                     margin: 0,
//                     fontFamily: "'Be Vietnam Pro', sans-serif",
//                   },
//                 },
//                 React.createElement(Space, null, [
//                   React.createElement(ShoppingCartOutlined, {
//                     key: 'icon',
//                     style: { color: '#3b82f6' },
//                   }),
//                   'Danh sách đơn hàng gần đây',
//                 ])
//               )
//             ),
//             recentOrders.length > 0
//               ? React.createElement(
//                   StyledTable,
//                   { key: 'table' },
//                   React.createElement(AntTable, {
//                     dataSource: recentOrders,
//                     columns: orderColumns,
//                     pagination: false,
//                     size: 'small',
//                     rowKey: 'id',
//                     scroll: { x: 1000 },
//                     locale: {
//                       emptyText: React.createElement(Empty, {
//                         image: Empty.PRESENTED_IMAGE_SIMPLE,
//                         description: React.createElement(
//                           'span',
//                           {
//                             style: {
//                               color: '#64748b',
//                               fontFamily: "'Be Vietnam Pro', sans-serif",
//                             },
//                           },
//                           'Không có dữ liệu'
//                         ),
//                       }),
//                     },
//                     style: {
//                       fontFamily: "'Be Vietnam Pro', sans-serif",
//                     },
//                   })
//                 )
//               : React.createElement(Empty, {
//                   key: 'empty',
//                   description: 'Không có dữ liệu',
//                 }),
//           ])
//         ),
//       ]
//     )
//   );
// };

// module.exports = Dashboard;
